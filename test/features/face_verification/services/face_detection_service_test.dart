import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceDetectionService', () {
    late FaceDetectionService faceDetectionService;

    setUp(() {
      faceDetectionService = FaceDetectionService();
    });

    tearDown(() {
      faceDetectionService.dispose();
    });

    group('initialize', () {
      test('should initialize face detector successfully', () async {
        // Act
        await faceDetectionService.initialize();

        // Assert
        expect(faceDetectionService.isInitialized, isTrue);
      });

      test('should handle multiple initialization calls', () async {
        // Act
        await faceDetectionService.initialize();
        await faceDetectionService.initialize(); // Second call

        // Assert
        expect(faceDetectionService.isInitialized, isTrue);
      });
    });

    group('configuration', () {
      test('should return configuration details', () async {
        // Arrange
        await faceDetectionService.initialize();

        // Act
        final config = faceDetectionService.getConfiguration();

        // Assert
        expect(config['isInitialized'], isTrue);
        expect(config['minimumCoverageThreshold'], equals(80));
      });
    });
  });
}
